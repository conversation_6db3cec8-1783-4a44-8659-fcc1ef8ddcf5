import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:audioplayers/audioplayers.dart';

// Enhanced device model for real Bluetooth functionality
class BluetoothDeviceModel {
  final String name;
  final String address;
  final bool isConnected;
  final bool hasAlarm;
  final BluetoothDevice? device; // Real flutter_blue_plus device
  final int rssi;
  final DateTime lastSeen;

  BluetoothDeviceModel({
    required this.name,
    required this.address,
    required this.isConnected,
    this.hasAlarm = false,
    this.device,
    this.rssi = 0,
    DateTime? lastSeen,
  }) : lastSeen = lastSeen ?? DateTime.now();

  BluetoothDeviceModel copyWith({
    String? name,
    String? address,
    bool? isConnected,
    bool? hasAlarm,
    BluetoothDevice? device,
    int? rssi,
    DateTime? lastSeen,
  }) {
    return BluetoothDeviceModel(
      name: name ?? this.name,
      address: address ?? this.address,
      isConnected: isConnected ?? this.isConnected,
      hasAlarm: hasAlarm ?? this.hasAlarm,
      device: device ?? this.device,
      rssi: rssi ?? this.rssi,
      lastSeen: lastSeen ?? this.lastSeen,
    );
  }

  // Create from flutter_blue_plus device
  factory BluetoothDeviceModel.fromBluetoothDevice(
    BluetoothDevice device,
    bool isConnected, {
    bool hasAlarm = false,
    int rssi = 0,
  }) {
    return BluetoothDeviceModel(
      name: device.platformName.isNotEmpty
          ? device.platformName
          : 'Unknown Device',
      address: device.remoteId.str,
      isConnected: isConnected,
      hasAlarm: hasAlarm,
      device: device,
      rssi: rssi,
    );
  }
}

class BluetoothService extends ChangeNotifier {
  List<BluetoothDeviceModel> _devices = [];
  final Map<String, bool> _alarmSettings = {};
  final Map<String, StreamSubscription> _connectionSubscriptions = {};
  StreamSubscription<BluetoothAdapterState>? _adapterStateSubscription;
  StreamSubscription<List<ScanResult>>? _scanSubscription;
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isScanning = false;
  bool _isInitialized = false;
  String _statusMessage = 'Initializing...';

  List<BluetoothDeviceModel> get devices => _devices;
  bool get isScanning => _isScanning;
  bool get isInitialized => _isInitialized;
  String get statusMessage => _statusMessage;

  bool hasAlarm(String address) => _alarmSettings[address] ?? false;

  // Initialize real Bluetooth functionality
  Future<void> initialize() async {
    try {
      _statusMessage = 'Checking Bluetooth permissions...';
      notifyListeners();

      // Check if Bluetooth is supported
      try {
        if (await FlutterBluePlus.isSupported == false) {
          _statusMessage = 'Bluetooth not supported on this device';
          notifyListeners();
          return;
        }
      } catch (e) {
        // Handle platform not supported (like in tests)
        _statusMessage = 'Bluetooth not available in current environment';
        _isInitialized = true;
        notifyListeners();
        return;
      }

      // Request permissions
      await _requestPermissions();

      // Listen to adapter state changes
      _adapterStateSubscription = FlutterBluePlus.adapterState.listen((state) {
        _handleAdapterStateChange(state);
      });

      // Get initial adapter state
      final adapterState = await FlutterBluePlus.adapterState.first;
      _handleAdapterStateChange(adapterState);

      // Load saved alarm settings
      await _loadAlarmSettings();

      _isInitialized = true;
      _statusMessage = 'Ready';
      notifyListeners();

      // Start initial scan
      await startScan();
    } catch (e) {
      _statusMessage = 'Error initializing Bluetooth: $e';
      debugPrint('Bluetooth initialization error: $e');
      notifyListeners();
    }
  }

  // Request necessary permissions for Bluetooth
  Future<void> _requestPermissions() async {
    if (Platform.isAndroid) {
      await [
        Permission.bluetoothScan,
        Permission.bluetoothConnect,
        Permission.bluetoothAdvertise,
        Permission.location,
      ].request();
    }
  }

  // Handle Bluetooth adapter state changes
  void _handleAdapterStateChange(BluetoothAdapterState state) {
    switch (state) {
      case BluetoothAdapterState.on:
        _statusMessage = 'Bluetooth is on';
        break;
      case BluetoothAdapterState.off:
        _statusMessage = 'Bluetooth is off - Please enable Bluetooth';
        break;
      case BluetoothAdapterState.turningOn:
        _statusMessage = 'Bluetooth is turning on...';
        break;
      case BluetoothAdapterState.turningOff:
        _statusMessage = 'Bluetooth is turning off...';
        break;
      default:
        _statusMessage = 'Bluetooth state unknown';
    }
    notifyListeners();
  }

  // Load saved alarm settings from SharedPreferences
  Future<void> _loadAlarmSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('alarm_'));
      for (final key in keys) {
        final address = key.substring(6); // Remove 'alarm_' prefix
        _alarmSettings[address] = prefs.getBool(key) ?? false;
      }
    } catch (e) {
      debugPrint('Error loading alarm settings: $e');
    }
  }

  // Save alarm setting to SharedPreferences
  Future<void> _saveAlarmSetting(String address, bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('alarm_$address', enabled);
    } catch (e) {
      debugPrint('Error saving alarm setting: $e');
    }
  }

  // Start scanning for Bluetooth devices
  Future<void> startScan() async {
    if (_isScanning) return;

    try {
      _isScanning = true;
      _statusMessage = 'Scanning for devices...';
      notifyListeners();

      // Clear previous devices
      _devices.clear();

      // Get connected devices first
      final connectedDevices = FlutterBluePlus.connectedDevices;
      for (final device in connectedDevices) {
        _addOrUpdateDevice(device, true);
      }

      // Start scanning for new devices
      _scanSubscription = FlutterBluePlus.scanResults.listen((results) {
        for (final result in results) {
          _addOrUpdateDevice(result.device, false, rssi: result.rssi);
        }
      });

      await FlutterBluePlus.startScan(
        timeout: const Duration(seconds: 10),
        androidUsesFineLocation: true,
      );

      _isScanning = false;
      _statusMessage = 'Scan completed';
      notifyListeners();
    } catch (e) {
      _isScanning = false;
      _statusMessage = 'Scan error: $e';
      debugPrint('Bluetooth scan error: $e');
      notifyListeners();
    }
  }

  // Add or update device in the list
  void _addOrUpdateDevice(BluetoothDevice device, bool isConnected,
      {int rssi = 0}) {
    final address = device.remoteId.str;
    final existingIndex = _devices.indexWhere((d) => d.address == address);

    final deviceModel = BluetoothDeviceModel.fromBluetoothDevice(
      device,
      isConnected,
      hasAlarm: _alarmSettings[address] ?? false,
      rssi: rssi,
    );

    if (existingIndex >= 0) {
      _devices[existingIndex] = deviceModel;
    } else {
      _devices.add(deviceModel);
    }

    // Monitor connection state for this device
    _monitorDeviceConnection(device);

    notifyListeners();
  }

  // Monitor individual device connection state
  void _monitorDeviceConnection(BluetoothDevice device) {
    final address = device.remoteId.str;

    // Cancel existing subscription if any
    _connectionSubscriptions[address]?.cancel();

    // Listen to connection state changes
    _connectionSubscriptions[address] = device.connectionState.listen((state) {
      final isConnected = state == BluetoothConnectionState.connected;
      _updateDeviceConnectionState(address, isConnected);
    });
  }

  // Update device connection state
  void _updateDeviceConnectionState(String address, bool isConnected) {
    final deviceIndex = _devices.indexWhere((d) => d.address == address);
    if (deviceIndex >= 0) {
      final oldDevice = _devices[deviceIndex];

      // Check if this is a disconnection and alarm is set
      if (oldDevice.isConnected && !isConnected && hasAlarm(address)) {
        _triggerDisconnectAlarm(oldDevice);
      }

      _devices[deviceIndex] = oldDevice.copyWith(isConnected: isConnected);
      notifyListeners();
    }
  }

  void toggleAlarm(String address) {
    _alarmSettings[address] = !(_alarmSettings[address] ?? false);

    // Update device list to reflect alarm status
    _devices = _devices.map((device) {
      if (device.address == address) {
        return device.copyWith(hasAlarm: _alarmSettings[address]);
      }
      return device;
    }).toList();

    // Save setting
    _saveAlarmSetting(address, _alarmSettings[address] ?? false);

    notifyListeners();

    // Show feedback
    if (_alarmSettings[address] == true) {
      _showAlarmSetMessage(address);
    }
  }

  void _showAlarmSetMessage(String address) {
    try {
      final device = _devices.firstWhere((d) => d.address == address);
      debugPrint(
          'Alarm set for ${device.name} - will trigger when disconnected');
    } catch (e) {
      debugPrint(
          'Alarm set for device $address - will trigger when disconnected');
    }
  }

  void _triggerDisconnectAlarm(BluetoothDeviceModel device) {
    debugPrint('🚨 ALARM: ${device.name} disconnected!');

    // Play system sound
    SystemSound.play(SystemSoundType.alert);

    // Play custom alarm sound
    _playAlarmSound();

    // In a real app, you could also:
    // - Show notification
    // - Vibrate device
    // - Send to background service
  }

  Future<void> _playAlarmSound() async {
    try {
      // Play system sound as fallback since we don't have a real audio file
      SystemSound.play(SystemSoundType.alert);

      // In a real implementation, you would use:
      // await _audioPlayer.play(AssetSource('sounds/alarm.mp3'));
    } catch (e) {
      debugPrint('Error playing alarm sound: $e');
    }
  }

  // Stop scanning
  Future<void> stopScan() async {
    if (_isScanning) {
      await FlutterBluePlus.stopScan();
      _isScanning = false;
      _statusMessage = 'Scan stopped';
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _scanSubscription?.cancel();
    _adapterStateSubscription?.cancel();
    for (final subscription in _connectionSubscriptions.values) {
      subscription.cancel();
    }
    _audioPlayer.dispose();
    super.dispose();
  }
}
