import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:flutter_application_3/main.dart';
import 'package:flutter_application_3/services/bluetooth_service_simple.dart';
import 'package:flutter_application_3/screens/main_screen.dart';

void main() {
  testWidgets('Main screen test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      ChangeNotifierProvider(
        create: (_) => BluetoothService(),
        child: const MaterialApp(
          home: MainScreen(),
        ),
      ),
    );

    await tester.pump();

    // Verify that basic elements are present
    expect(find.text('Bluetooth Alarm'), findsOneWidget);
    expect(find.text('Bluetooth Devices'), findsOneWidget);
    expect(find.text('Toggle switch to set disconnect alarm'), findsOneWidget);
  });

  testWidgets('App test', (WidgetTester tester) async {
    // Test the main app
    await tester.pumpWidget(const BluetoothAlarmApp());
    await tester.pump();

    // Verify app loads without errors
    expect(find.byType(MaterialApp), findsOneWidget);
  });

  testWidgets('Bluetooth service initialization test',
      (WidgetTester tester) async {
    final bluetoothService = BluetoothService();

    // Test initial state
    expect(bluetoothService.devices, isEmpty);
    expect(bluetoothService.isScanning, false);
    expect(bluetoothService.isInitialized, false);
    expect(bluetoothService.statusMessage, 'Initializing...');
  });

  testWidgets('Device alarm toggle test', (WidgetTester tester) async {
    final bluetoothService = BluetoothService();
    const testAddress = '00:11:22:33:44:55';

    // Test initial alarm state
    expect(bluetoothService.hasAlarm(testAddress), false);

    // Toggle alarm
    bluetoothService.toggleAlarm(testAddress);
    expect(bluetoothService.hasAlarm(testAddress), true);

    // Toggle again
    bluetoothService.toggleAlarm(testAddress);
    expect(bluetoothService.hasAlarm(testAddress), false);
  });
}
